package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.MasterComponentBean;
import com.heal.controlcenter.beans.MasterComponentTypeBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.pojo.Controller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * DAO for master component operations.
 */
@Slf4j
@Repository
public class MasterComponentDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets component details with name and version.
     * @param componentName The component name
     * @param componentVersion The component version
     * @param accountId The account ID
     * @return MasterComponentBean if found, null otherwise
     */
    public MasterComponentBean getComponentDetailsWithNameAndVersion(String componentName, String componentVersion, int accountId) {
        String sql = "SELECT vc.component_id AS id, vc.component_name AS name, vc.is_custom AS isCustom, " +
                "vc.component_status AS status, mc.created_time AS createdTime, mc.updated_time AS updatedTime, " +
                "mc.user_details_id AS userDetailsId, mc.account_id AS accountId, mc.description AS description, " +
                "vc.component_type_name AS componentTypeName, vc.component_version_name AS componentVersionName, " +
                "vc.component_version_id AS componentVersionId, vc.component_type_id AS componentTypeId, " +
                "vc.common_version_name AS commonVersionName, vc.common_version_id AS commonVersionId " +
                "FROM view_components vc " +
                "JOIN mst_component mc ON vc.component_id = mc.id " +
                "JOIN mst_component_attribute_mapping mcam ON vc.component_id = mcam.mst_component_id " +
                "AND vc.common_version_id = mcam.mst_common_version_id " +
                "AND vc.component_type_id = mcam.mst_component_type_id " +
                "WHERE mc.account_id IN (1, ?) " +
                "AND vc.component_type_status = 1 " +
                "AND vc.component_status = 1 " +
                "AND vc.is_version_status = 1 " +
                "AND vc.component_name = ? " +
                "AND vc.component_version_name = ?";

        try {
            List<MasterComponentBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                MasterComponentBean bean = new MasterComponentBean();
                bean.setId(rs.getInt("id"));
                bean.setName(rs.getString("name"));
                bean.setIsCustom(rs.getInt("isCustom"));
                bean.setStatus(rs.getInt("status"));
                bean.setCreatedTime(rs.getString("createdTime"));
                bean.setUpdatedTime(rs.getString("updatedTime"));
                bean.setUserDetailsId(rs.getString("userDetailsId"));
                bean.setAccountId(rs.getInt("accountId"));
                bean.setDescription(rs.getString("description"));
                bean.setComponentTypeName(rs.getString("componentTypeName"));
                bean.setComponentVersionName(rs.getString("componentVersionName"));
                bean.setComponentVersionId(rs.getInt("componentVersionId"));
                bean.setComponentTypeId(rs.getInt("componentTypeId"));
                bean.setCommonVersionName(rs.getString("commonVersionName"));
                bean.setCommonVersionId(rs.getInt("commonVersionId"));
                return bean;
            }, accountId, componentName, componentVersion);

            return results.stream()
                    .filter(c -> c.getName().equals(componentName) && c.getComponentVersionName().equals(componentVersion))
                    .findAny()
                    .orElse(null);
        } catch (Exception e) {
            log.error("Error getting component details: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets master component type using name.
     * @param componentTypeName The component type name
     * @param accountId The account ID
     * @return MasterComponentTypeBean if found, null otherwise
     */
    public MasterComponentTypeBean getMasterComponentTypeUsingName(String componentTypeName, String accountId) {
        String sql = "SELECT id, name, description, is_custom as isCustom, status, " +
                "created_time as createdTime, updated_time as updatedTime, " +
                "user_details_id as userDetailsId, account_id as accountId " +
                "FROM mst_component_type " +
                "WHERE account_id IN (1, ?) AND LOWER(name) = LOWER(?)";

        try {
            List<MasterComponentTypeBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                MasterComponentTypeBean bean = new MasterComponentTypeBean();
                bean.setId(rs.getInt("id"));
                bean.setName(rs.getString("name"));
                bean.setDescription(rs.getString("description"));
                bean.setIsCustom(rs.getInt("isCustom"));
                bean.setStatus(rs.getInt("status"));
                bean.setCreatedTime(rs.getString("createdTime"));
                bean.setUpdatedTime(rs.getString("updatedTime"));
                bean.setUserDetailsId(rs.getString("userDetailsId"));
                bean.setAccountId(rs.getInt("accountId"));
                return bean;
            }, Integer.parseInt(accountId), componentTypeName);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting component type: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets all controllers for account.
     * @param accountId The account ID
     * @return List of Controller objects
     */
    public List<Controller> getControllerList(int accountId) {
        String sql = "SELECT id as appId, name, controller_type_id as controllerTypeId, identifier, " +
                "plugin_supr_interval as pluginSuppressionInterval, plugin_whitelist_status as pluginWhitelisted, " +
                "status, user_details_id as createdBY, created_time as createdOn, " +
                "updated_time as updatedTime, account_id as accountId " +
                "FROM controller WHERE account_id = ? AND status = 1";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                Controller controller = new Controller();
                controller.setAppId(rs.getString("appId"));
                controller.setName(rs.getString("name"));
                controller.setControllerTypeId(rs.getInt("controllerTypeId"));
                controller.setIdentifier(rs.getString("identifier"));
                controller.setPluginSuppressionInterval(rs.getInt("pluginSuppressionInterval"));
                controller.setPluginWhitelisted(rs.getBoolean("pluginWhitelisted"));
                controller.setStatus(rs.getInt("status"));
                controller.setCreatedBY(rs.getString("createdBY"));
                controller.setCreatedOn(rs.getString("createdOn"));
                controller.setUpdatedTime(rs.getString("updatedTime"));
                controller.setAccountId(rs.getInt("accountId"));
                return controller;
            }, accountId);
        } catch (Exception e) {
            log.error("Error getting controller list for account: {}", accountId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets master type for subtype name.
     * @param typeName The type name
     * @param subTypeName The subtype name
     * @param accountId The account ID
     * @return ViewTypes if found, null otherwise
     */
    public ViewTypesBean getMstTypeForSubTypeName(String typeName, String subTypeName, int accountId) {
        String sql = "SELECT mt.id as typeId, mt.name as typeName, mst.sub_type_id as subTypeId, mst.sub_type_name as subTypeName " +
                "FROM mst_type mt " +
                "JOIN mst_sub_type mst ON mt.id = mst.mst_type_id " +
                "WHERE LOWER(mt.name) = LOWER(?) AND LOWER(mst.sub_type_name) = LOWER(?) " +
                "AND mst.account_id IN (1, ?)";

        @SqlQuery("select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types ")
        List<ViewTypes> getAllTypes();

        try {
            List<ViewTypesBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                ViewTypesBean viewType = new ViewTypesBean();
                viewType.setTypeId(rs.getInt("typeId"));
                viewType.setTypeName(rs.getString("typeName"));
                viewType.setSubTypeId(rs.getInt("subTypeId"));
                viewType.setSubTypeName(rs.getString("subTypeName"));
                return viewType;
            }, accountId, typeName.trim(), subTypeName.trim());

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type view from DB. Reason: {}", e.getMessage());
            return null;
        }
    }
}
